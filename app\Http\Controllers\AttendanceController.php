<?php

namespace App\Http\Controllers;

use App\Models\AttendanceLog;
use App\Models\Employee;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Log;

class AttendanceController extends Controller
{
    public function index()
    {
        return Inertia::render('Scan');
    }
    
    public function lookupEmployee($bioId) {
        $employee = Employee::where('EMP_BioID', $bioId)->first();
        if (!$employee)  {
            return response()->json([
                'success' => false,
                'message' => 'Employee not found'
            ], 404);
        }

        // Return employee data regardless of active status
        // Frontend will handle inactive employee validation and show appropriate warnings

        $lastAction = $employee->lastAttendanceAction();
        $currentState = $lastAction ? $lastAction->action_type : null; // Using numeric action types: 1=check_in, 2=check_out, 3=break_in, 4=break_out

        $allowedAction = ['check_in', 'check_out', 'break_in', 'break_out'];

        return response()->json([
            'success' => true,
            'employee' => [
                'EMP_EmpNo' => $employee->EMP_EmpNo,
                'EMP_BioID' => $employee->EMP_BioID,
                'EMP_EmpID' => $employee->EMP_EmpID,
                'EMP_FullName' => $employee->EMP_FullName,
                'EMP_FirstName' => $employee->EMP_FirstName,
                'EMP_LastName' => $employee->EMP_LastName,
                'EMP_Department' => $employee->EMP_Department,
                'EMP_IsActive' => $employee->EMP_IsActive,
                // 'EMP_Status' => $employee->status,
                'EMP_PhotoPath' => $employee->photo_url,
                'last_action' => $lastAction ? [
                    'action_type' => $lastAction->action_type,
                    'logged_at' => $lastAction->logged_at->format('M d, Y g:i A'),
                    'action_label' => $lastAction->action_label
                ] : null,
                'is_checked_in' => $employee->isCheckedIn(),
                'current_state' => $currentState,
                'allowed_actions' => $allowedAction
            ]
        ]);
    }

    public function store(Request $request) {
        // Debug logging
        Log::info('AttendanceController::store called', [
            'request_data' => $request->all(),
            'method' => $request->method(),
            'url' => $request->url(),
        ]);

        $request->validate ([
            'employee_code' => 'required|string|min:6',
            'Emp_Type' => 'required|in:check_in,check_out,break_in,break_out',
        ]);

        $employee = Employee::where('EMP_BioID', $request->employee_code)->first();

        if(!$employee){
             return redirect()->back()->with('error', 'Employee not found with barcode: ' . $request->employee_code);
        }

      

        // Check if employee is active (EMP_IsActive=1)
        if (!$employee->isActive()) {
            return redirect()->back()->with('error', 'Employee is inactive and cannot perform attendance actions.');
        }

        // Map action types to numeric IDs (1=check_in, 2=check_out, 3=break_in, 4=break_out)
        $actionTypeMap = [
            'check_in' => 1,
            'check_out' => 2,
            'break_in' => 3,
            'break_out' => 4,
        ];

        $actionTypeId = $actionTypeMap[$request->Emp_Type] ?? null;

        if (!$actionTypeId) {
            return redirect()->back()->with('error', 'Invalid action type: ' . $request->Emp_Type);
        }

        Log::info('Creating attendance log', [
            'employee_no' => $employee->EMP_EmpNo,
            'bio_id' => $employee->EMP_BioID,
            'action_type_id' => $actionTypeId,
            'action_type_string' => $request->Emp_Type,
        ]);

        try {
            $log = AttendanceLog::create([
                'EMP_EmpNo' => $employee->EMP_EmpNo,
                'EMP_BioID' => $employee->EMP_BioID,
                'action_type' => $actionTypeId,
                'logged_at' => now(),
            ]);

            Log::info('Attendance log created successfully', [
                'log_id' => $log->id,
                'action_type' => $log->action_type,
                'logged_at' => $log->logged_at,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create attendance log', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return redirect()->back()->with('error', 'Failed to record attendance: ' . $e->getMessage());
        }

        // Map numeric action types back to labels
        $actionLabels = [
            1 => 'Check In',
            2 => 'Check Out',
            3 => 'Break In',
            4 => 'Break Out',
        ];

        $actionLabel = $actionLabels[$actionTypeId] ?? 'Unknown Action';
        $timeFormatted = $log->logged_at->format('g:i A');

        return redirect()->back()->with('success', "{$employee->EMP_FullName} - {$actionLabel} recorded successfully at {$timeFormatted}.");
    }


    /**
     * Get allowed actions - now returns all actions since there are no restrictions
     */
    private function getAllowedActions($currentState): array
    {
        // Return all possible actions - no restrictions
        return ['check_in', 'check_out', 'break_in', 'break_out'];
    }

    /**
     * Get action label for display
     */
    private function getActionLabel($actionType): string
    {
        return match($actionType) {
            'check_in' => 'Check In',
            'check_out' => 'Check Out',
            'break_in' => 'Break In',
            'break_out' => 'Break Out',
            default => ucfirst(str_replace('_', ' ', $actionType))
        };
    }

    /**
     * Display attendance logs
     */
    public function logs(Request $request)
    {
        $query = AttendanceLog::with('employee');

        // Only show Check In (1) and Check Out (2) actions
        $query->whereIn('action_type', [1, 2]);

        // Filter by date range (convert to Clarion format)
        if ($request->filled('date_from')) {
            $clarionDateFrom = AttendanceLog::toClarionDate(Carbon::parse($request->date_from));
            $query->where('log_date', '>=', $clarionDateFrom);
        }

        if ($request->filled('date_to')) {
            $clarionDateTo = AttendanceLog::toClarionDate(Carbon::parse($request->date_to));
            $query->where('log_date', '<=', $clarionDateTo);
        }

        // Default to last 7 days if no date range specified
        if (!$request->filled('date_from') && !$request->filled('date_to')) {
            $clarionSevenDaysAgo = AttendanceLog::toClarionDate(now()->subDays(7));
            $clarionToday = AttendanceLog::toClarionDate(today());
            $query->whereBetween('log_date', [$clarionSevenDaysAgo, $clarionToday]);
        }

        // Filter by multiple employees
        if ($request->filled('employees') && is_array($request->employees)) {
            $validEmployees = array_filter($request->employees, 'is_numeric');
            if (!empty($validEmployees)) {
                $query->whereIn('EMP_EmpNo', $validEmployees);
            }
        }

        // Additional action type filtering (within check-in/check-out only)
        if ($request->filled('action_type') && is_numeric($request->action_type)) {
            $actionType = (int) $request->action_type;
            if (in_array($actionType, [1, 2])) { // Only allow check-in/check-out
                $query->where('action_type', $actionType);
            }
        }

        $logs = $query->orderBy('logged_at', 'desc')
                     ->paginate(100)
                     ->withQueryString();

        // Transform logs data to include calculated fields
        $logs->getCollection()->transform(function ($log) {
            // The action_label accessor is already available on the model
            return $log;
        });

        $employees = Employee::where('EMP_IsActive', 1)
                            ->orderBy('EMP_FirstName')
                            ->get(['EMP_EmpNo', 'EMP_FullName', 'EMP_PhotoPath']);

        // Only show Check In and Check Out action types
        $actionTypes = [
            ['action_type' => 1, 'action_label' => 'Check In'],
            ['action_type' => 2, 'action_label' => 'Check Out'],
        ];

        // Generate merged sessions and hours summary
        $mergedSessions = $this->generateMergedSessions($request);
        $hoursWorkedSummary = $this->generateHoursWorkedSummary($request);

        return Inertia::render('Attendance/Logs', [
            'logs' => $logs,
            'mergedSessions' => $mergedSessions,
            'employees' => $employees,
            'actionTypes' => $actionTypes,
            'hoursWorkedSummary' => $hoursWorkedSummary,
            'filters' => $request->only(['date_from', 'date_to', 'employees', 'action_type']),
        ]);
    }

    /**
     * Generate merged sessions with paired check-in/check-out records
     */
    private function generateMergedSessions(Request $request): array
    {
        $query = AttendanceLog::with('employee')
                             ->whereIn('action_type', [1, 2]); // Only check-in/check-out

        // Apply same date filtering as main query
        $this->applyDateFiltering($query, $request);
        $this->applyEmployeeFiltering($query, $request);

        $logs = $query->orderBy('EMP_EmpNo')
                     ->orderBy('logged_at')
                     ->get();

        // Group by employee and merge sessions
        $sessions = [];
        $employeeLogs = $logs->groupBy('EMP_EmpNo');

        foreach ($employeeLogs as $empNo => $empLogs) {
            $employee = $empLogs->first()->employee;
            $checkIn = null;

            foreach ($empLogs as $log) {
                if ($log->action_type == 1) { // Check In
                    $checkIn = $log;
                } elseif ($log->action_type == 2 && $checkIn) { // Check Out with matching Check In
                    $duration = $checkIn->logged_at->diffInMinutes($log->logged_at);
                    $sessions[] = [
                        'employee' => $employee,
                        'check_in' => $checkIn,
                        'check_out' => $log,
                        'duration_minutes' => $duration,
                        'duration_hours' => round($duration / 60, 2),
                        'date' => $checkIn->logged_at->format('Y-m-d'),
                        'is_complete' => true
                    ];
                    $checkIn = null; // Reset for next pair
                } elseif ($log->action_type == 2 && !$checkIn) { // Unpaired Check Out
                    $sessions[] = [
                        'employee' => $employee,
                        'check_in' => null,
                        'check_out' => $log,
                        'duration_minutes' => 0,
                        'duration_hours' => 0,
                        'date' => $log->logged_at->format('Y-m-d'),
                        'is_complete' => false
                    ];
                }
            }

            // Handle unpaired check-in at the end
            if ($checkIn) {
                $sessions[] = [
                    'employee' => $employee,
                    'check_in' => $checkIn,
                    'check_out' => null,
                    'duration_minutes' => 0,
                    'duration_hours' => 0,
                    'date' => $checkIn->logged_at->format('Y-m-d'),
                    'is_complete' => false
                ];
            }
        }

        return $sessions;
    }

    /**
     * Generate hours worked summary for employees
     */
    private function generateHoursWorkedSummary(Request $request): array
    {
        $sessions = $this->generateMergedSessions($request);
        $summary = [];

        foreach ($sessions as $session) {
            $empNo = $session['employee']->EMP_EmpNo;

            if (!isset($summary[$empNo])) {
                $summary[$empNo] = [
                    'employee' => $session['employee'],
                    'total_hours' => 0,
                    'total_sessions' => 0,
                    'complete_sessions' => 0,
                    'incomplete_sessions' => 0,
                    'daily_breakdown' => []
                ];
            }

            $summary[$empNo]['total_sessions']++;

            if ($session['is_complete']) {
                $summary[$empNo]['total_hours'] += $session['duration_hours'];
                $summary[$empNo]['complete_sessions']++;

                // Daily breakdown
                $date = $session['date'];
                if (!isset($summary[$empNo]['daily_breakdown'][$date])) {
                    $summary[$empNo]['daily_breakdown'][$date] = 0;
                }
                $summary[$empNo]['daily_breakdown'][$date] += $session['duration_hours'];
            } else {
                $summary[$empNo]['incomplete_sessions']++;
            }
        }

        return array_values($summary);
    }

    /**
     * Apply date filtering to query
     */
    private function applyDateFiltering($query, Request $request): void
    {
        if ($request->filled('date_from')) {
            $clarionDateFrom = AttendanceLog::toClarionDate(Carbon::parse($request->date_from));
            $query->where('log_date', '>=', $clarionDateFrom);
        }

        if ($request->filled('date_to')) {
            $clarionDateTo = AttendanceLog::toClarionDate(Carbon::parse($request->date_to));
            $query->where('log_date', '<=', $clarionDateTo);
        }

        if (!$request->filled('date_from') && !$request->filled('date_to')) {
            $clarionSevenDaysAgo = AttendanceLog::toClarionDate(now()->subDays(7));
            $clarionToday = AttendanceLog::toClarionDate(today());
            $query->whereBetween('log_date', [$clarionSevenDaysAgo, $clarionToday]);
        }
    }

    /**
     * Apply employee filtering to query
     */
    private function applyEmployeeFiltering($query, Request $request): void
    {
        if ($request->filled('employees') && is_array($request->employees)) {
            $validEmployees = array_filter($request->employees, 'is_numeric');
            if (!empty($validEmployees)) {
                $query->whereIn('EMP_EmpNo', $validEmployees);
            }
        }
    }

    /**
     * Export consolidated attendance hours to Excel
     */
    public function exportExcel(Request $request)
    {
        // Get merged sessions data
        $sessions = $this->generateMergedSessions($request);
        $consolidatedData = $this->generateConsolidatedHours($sessions);

        $filename = 'consolidated_hours_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($consolidatedData, $request) {
            $file = fopen('php://output', 'w');

            // Add header with date range info
            $dateRange = '';
            if ($request->filled('date_from') && $request->filled('date_to')) {
                $dateRange = "Date Range: {$request->date_from} to {$request->date_to}";
            } elseif ($request->filled('date_from')) {
                $dateRange = "From: {$request->date_from}";
            } elseif ($request->filled('date_to')) {
                $dateRange = "Until: {$request->date_to}";
            } else {
                $dateRange = "Date Range: Last 7 days";
            }

            fputcsv($file, [$dateRange]);
            fputcsv($file, []); // Empty row

            // CSV Headers for Consolidated Hours
            fputcsv($file, [
                'Employee Name',
                'Employee ID',
                'Total Hours Worked',
                'Total Sessions',
                'Working Days',
                'Average Hours per Day'
            ]);

            // CSV Data for Consolidated Hours
            foreach ($consolidatedData as $data) {
                fputcsv($file, [
                    $data['employee']->EMP_FullName ?? 'Unknown',
                    $data['employee']->EMP_EmpNo,
                    round($data['total_hours'], 2),
                    $data['session_count'],
                    $data['working_days'],
                    round($data['average_hours_per_day'], 2)
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Generate consolidated hours data from sessions
     */
    private function generateConsolidatedHours(array $sessions): array
    {
        $employeeData = [];

        foreach ($sessions as $session) {
            $empNo = $session['employee']->EMP_EmpNo;

            if (!isset($employeeData[$empNo])) {
                $employeeData[$empNo] = [
                    'employee' => $session['employee'],
                    'total_hours' => 0,
                    'session_count' => 0,
                    'working_days' => [],
                    'sessions' => []
                ];
            }

            $empData = &$employeeData[$empNo];
            $empData['sessions'][] = $session;
            $empData['session_count']++;

            // Only count complete sessions for hours
            if ($session['is_complete'] && $session['duration_hours'] > 0) {
                $empData['total_hours'] += $session['duration_hours'];
            }

            // Track unique working days
            if ($session['date'] && !in_array($session['date'], $empData['working_days'])) {
                $empData['working_days'][] = $session['date'];
            }
        }

        // Convert to final format and calculate averages
        $result = [];
        foreach ($employeeData as $empData) {
            $workingDaysCount = count($empData['working_days']);
            $result[] = [
                'employee' => $empData['employee'],
                'total_hours' => $empData['total_hours'],
                'session_count' => $empData['session_count'],
                'working_days' => $workingDaysCount,
                'average_hours_per_day' => $workingDaysCount > 0
                    ? $empData['total_hours'] / $workingDaysCount
                    : 0,
                'sessions' => $empData['sessions']
            ];
        }

        // Sort by total hours descending
        usort($result, function($a, $b) {
            return $b['total_hours'] <=> $a['total_hours'];
        });

        return $result;
    }

    /**
     * Display attendance logs for a specific employee
     */
    public function employeeLogs(Request $request, Employee $employee)
    {
        $query = $employee->attendanceLogs();

        // Filter by date range (convert to Clarion format)
        if ($request->filled('start_date')) {
            $clarionStartDate = AttendanceLog::toClarionDate(Carbon::parse($request->start_date));
            $query->where('log_date', '>=', $clarionStartDate);
        }

        if ($request->filled('end_date')) {
            $clarionEndDate = AttendanceLog::toClarionDate(Carbon::parse($request->end_date));
            $query->where('log_date', '<=', $clarionEndDate);
        }

        // Default to last 30 days if no date filter
        if (!$request->filled('start_date') && !$request->filled('end_date')) {
            $clarionThirtyDaysAgo = AttendanceLog::toClarionDate(now()->subDays(30));
            $query->where('log_date', '>=', $clarionThirtyDaysAgo);
        }

        $logs = $query->orderBy('logged_at', 'desc')
                     ->paginate(100)
                     ->withQueryString();

        return Inertia::render('Attendance/EmployeeLogs', [
            'employee' => $employee,
            'logs' => $logs,
            'filters' => $request->only(['start_date', 'end_date']),
        ]);
    }
}

