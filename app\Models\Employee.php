<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Employee extends Model
{
    protected $table = 'Employee';
    protected $primaryKey = 'EMP_EmpNo';

    protected $fillable = [
        'EMP_EmpID',
        'EMP_BioID',
        'EMP_FirstName',
        'EMP_LastName',
        'EMP_MiddleName',
        'EMP_FullName',
        'EMP_Department',
        'EMP_IsActive',
        'EMP_Type',
        'EMP_PhotoPath',
    ];

    protected $casts = [
        'EMP_IsActive' => 'integer',
    ];

    /**
     * Boot method to auto-generate EMP_BioID
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($employee) {
            if (empty($employee->EMP_BioID)) {
                $employee->EMP_BioID = self::generateBioID();
            }

            // Auto-generate full name if not provided
            if (empty($employee->EMP_FullName)) {
                $employee->EMP_FullName = trim(
                    $employee->EMP_FirstName . ' ' .
                    $employee->EMP_MiddleName . ' ' .
                    $employee->EMP_LastName
                );
            }
        });
    }

    /**
     * Generate unique BioID for barcode
     */
    public static function generateBioID(): string
    {
        do {
            // Generate a 8-digit numeric ID with prefix
            $bioId = 'EMP' . str_pad(mt_rand(10000, 99999), 5, '0', STR_PAD_LEFT);
        } while (self::where('EMP_BioID', $bioId)->exists());

        return $bioId;
    }

    /**
     * Get attendance logs for this employee
     */
    public function attendanceLogs(): HasMany
    {
        return $this->hasMany(AttendanceLog::class, 'EMP_EmpNo', 'EMP_EmpNo');
    }

    /**
     * Get today's attendance logs
     */
    public function todayLogs()
    {
        $clarionToday = AttendanceLog::toClarionDate(today());
        return $this->attendanceLogs()
            ->where('log_date', $clarionToday)
            ->orderBy('logged_at');
    }

    /**
     * Get the last attendance action
     */
    public function lastAttendanceAction()
    {
        return $this->attendanceLogs()
            ->latest('logged_at')
            ->first();
    }

    /**
     * Check if employee is currently checked in
     */
    public function isCheckedIn(): bool
    {
        $lastAction = $this->lastAttendanceAction();
        if (!$lastAction) {
            return false;
        }

        // Using numeric action types: 1=check_in, 3=break_in
        return in_array($lastAction->action_type, [1, 3]);
    }

    /**
     * Check if employee is active (can perform attendance actions)
     */
    public function isActive(): bool
    {
        return $this->EMP_IsActive === 1;
    }

    /**
     * Check if employee can perform attendance actions
     */
    public function canPerformAttendanceActions(): bool
    {
        return $this->isActive();
    }

    /**
     * Get employee status as string for display
     */
    public function getStatusAttribute(): string
    {
        return $this->EMP_IsActive === 1 ? 'active' : 'inactive';
    }

    /**
     * Get full name attribute
     */
    public function getFullNameAttribute(): string
    {
        return $this->EMP_FullName ?: trim($this->EMP_FirstName . ' ' . $this->EMP_MiddleName . ' ' . $this->EMP_LastName);
    }

    /**
     * Get employee photo URL
     */
    public function getPhotoUrlAttribute(): ?string
    {
        // If there's a photo path stored, return the full URL
        if (!empty($this->EMP_PhotoPath)) {
            // If it's already a full URL, return as is
            if (filter_var($this->EMP_PhotoPath, FILTER_VALIDATE_URL)) {
                return $this->EMP_PhotoPath;
            }
            
            // Handle full storage path (storage\app\public\filename.jpg)
            if (strpos($this->EMP_PhotoPath, 'storage\\app\\public\\') === 0) {
                $filename = str_replace('storage\\app\\public\\', '', $this->EMP_PhotoPath);
                return $this->getPhotoAssetUrl($filename);
            }
            
            // Handle storage/app/public/ path (Unix style)
            if (strpos($this->EMP_PhotoPath, 'storage/app/public/') === 0) {
                $filename = str_replace('storage/app/public/', '', $this->EMP_PhotoPath);
                return $this->getPhotoAssetUrl($filename);
            }
            
            // If it's a relative path, convert to full URL
            return $this->getPhotoAssetUrl(ltrim($this->EMP_PhotoPath, '/'));
        }

        // Return null if no photo - let the frontend handle placeholder
        return null;
    }

    /**
     * Get photo asset URL with case-insensitive file checking
     */
    private function getPhotoAssetUrl($filename): string
    {
        $storagePath = storage_path('app/public');
        
        // First try exact match
        if (file_exists($storagePath . '/' . $filename)) {
            return asset('storage/' . $filename);
        }
        
        // Try case-insensitive match
        $files = glob($storagePath . '/' . strtolower($filename));
        if (empty($files)) {
            $files = glob($storagePath . '/' . strtoupper($filename));
        }
        if (empty($files)) {
            // Try with different case extensions
            $pathInfo = pathinfo($filename);
            $baseName = $pathInfo['filename'];
            $extension = $pathInfo['extension'] ?? '';
            
            if ($extension) {
                $upperExt = $baseName . '.' . strtoupper($extension);
                $lowerExt = $baseName . '.' . strtolower($extension);
                
                if (file_exists($storagePath . '/' . $upperExt)) {
                    return asset('storage/' . $upperExt);
                }
                if (file_exists($storagePath . '/' . $lowerExt)) {
                    return asset('storage/' . $lowerExt);
                }
            }
        } else {
            $actualFilename = basename($files[0]);
            return asset('storage/' . $actualFilename);
        }
        
        // Fallback to original filename even if file doesn't exist
        return asset('storage/' . $filename);
    }

    /**
     * Generate barcode for this employee
     */
    public function generateBarcode($type = 'CODE128', $width = 2, $height = 100): string
    {
        // For now, return a simple barcode representation
        return $this->getSimpleBarcodeSVG($width, $height);
    }

    /**
     * Get barcode as PNG
     */
    public function getBarcodeImage($type = 'CODE128', $width = 2, $height = 100): string
    {
        // Return empty string for now - PNG generation requires more complex setup
        return '';
    }

    /**
     * Get barcode HTML for display
     */
    public function getBarcodeHtml($type = 'CODE128', $width = 2, $height = 100): string
    {
        return '<div style="text-align: center; padding: 20px; border: 1px solid #ddd; background: white;">' .
               $this->getSimpleBarcodeSVG($width, $height) .
               '</div>';
    }

    /**
     * Generate a simple barcode SVG representation
     */
    private function getSimpleBarcodeSVG($width = 2, $height = 100): string
    {
        if (empty($this->EMP_BioID)) {
            return $this->getFallbackBarcodeSVG('No BioID');
        }

        // Create a simple barcode pattern based on the BioID
        $bioId = $this->EMP_BioID;
        $barcodeWidth = strlen($bioId) * 12 * $width; // Approximate width
        $barcodeHeight = $height;

        $svg = '<svg width="' . $barcodeWidth . '" height="' . ($barcodeHeight + 40) . '" xmlns="http://www.w3.org/2000/svg">';
        $svg .= '<rect width="' . $barcodeWidth . '" height="' . ($barcodeHeight + 40) . '" fill="white"/>';

        // Generate simple barcode pattern
        $x = 0;
        for ($i = 0; $i < strlen($bioId); $i++) {
            $char = $bioId[$i];
            $charCode = ord($char);

            // Create a pattern based on character code
            for ($j = 0; $j < 8; $j++) {
                $bit = ($charCode >> $j) & 1;
                $barWidth = $width * ($bit ? 2 : 1);

                if ($bit) {
                    $svg .= '<rect x="' . $x . '" y="10" width="' . $barWidth . '" height="' . $barcodeHeight . '" fill="black"/>';
                }

                $x += $barWidth + 1;
            }
        }

        // Add text below barcode
        $svg .= '<text x="' . ($barcodeWidth / 2) . '" y="' . ($barcodeHeight + 30) . '" text-anchor="middle" font-family="monospace" font-size="14" fill="black">' . htmlspecialchars($bioId) . '</text>';
        $svg .= '</svg>';

        return $svg;
    }

    /**
     * Get fallback SVG when barcode generation fails
     */
    private function getFallbackBarcodeSVG($errorMessage = 'Barcode Error'): string
    {
        return '<svg width="300" height="120" xmlns="http://www.w3.org/2000/svg">
            <rect width="300" height="120" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
            <text x="150" y="40" text-anchor="middle" font-family="Arial" font-size="14" fill="#6c757d">Barcode Placeholder</text>
            <text x="150" y="65" text-anchor="middle" font-family="monospace" font-size="16" fill="#333">' . htmlspecialchars($this->EMP_BioID ?? 'No ID') . '</text>
            <text x="150" y="85" text-anchor="middle" font-family="Arial" font-size="10" fill="#dc3545">' . htmlspecialchars($errorMessage) . '</text>
            <text x="150" y="105" text-anchor="middle" font-family="Arial" font-size="8" fill="#6c757d">Scan the text above with your scanner</text>
        </svg>';
    }
}
