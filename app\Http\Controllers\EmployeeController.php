<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use Illuminate\Http\Request;
use Inertia\Inertia;

class EmployeeController extends Controller
{
   
    public function index(Request $request)
    {
        $query = Employee::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('EMP_FirstName', 'like', "%{$search}%")
                  ->orWhere('EMP_LastName', 'like', "%{$search}%")
                  ->orWhere('EMP_FullName', 'like', "%{$search}%")
                  ->orWhere('EMP_BioID', 'like', "%{$search}%")
                  ->orWhere('EMP_EmpNo', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('EMP_IsActive', 1);
            } elseif ($request->status === 'inactive') {
                $query->where('EMP_IsActive', 0);
            }
        }

        $employees = $query->orderBy('EMP_FirstName')
                          ->paginate(20)
                          ->withQueryString();

        return Inertia::render('Employees/Index', [
            'employees' => $employees,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

   
    public function create()
    {
        return Inertia::render('Employees/Create');
    }

  
    public function store(Request $request)
    {
        $validated = $request->validate([
            'EMP_EmpNo' => 'nullable|string|unique:Employee,EMP_EmpNo',
            'EMP_FirstName' => 'required|string|max:255',
            'EMP_LastName' => 'required|string|max:255',
            'EMP_MiddleName' => 'nullable|string|max:255',
            'EMP_Department' => 'nullable|string|max:255',
            'EMP_IsActive' => 'required|integer|in:0,1',
        ]);

        $employee = Employee::create($validated);

        return redirect()->route('employees.show', $employee)
                        ->with('success', 'Employee created successfully with barcode: ' . $employee->EMP_BioID);
    }

    /**
     * Display the specified employee
     */
    public function show(Employee $employee)
    {
        $employee->load(['attendanceLogs' => function ($query) {
            $query->latest('logged_at')->take(10);
        }]);

        return Inertia::render('Employees/Show', [
            'employee' => $employee,
            'recentLogs' => $employee->attendanceLogs,
        ]);
    }

    /**
     * Show the form for editing the specified employee
     */
    public function edit(Employee $employee)
    {
        return Inertia::render('Employees/Edit', [
            'employee' => $employee,
        ]);
    }

    /**
     * Update the specified employee
     */
    public function update(Request $request, Employee $employee)
    {
        $validated = $request->validate([
            'EMP_EmpNo' => "nullable|string|unique:Employee,EMP_EmpNo,{$employee->EMP_EmpID},EMP_EmpID",
            'EMP_FirstName' => 'required|string|max:255',
            'EMP_LastName' => 'required|string|max:255',
            'EMP_MiddleName' => 'nullable|string|max:255',
            'EMP_Department' => 'nullable|string|max:255',
            'EMP_IsActive' => 'required|integer|in:0,1',
        ]);

        $employee->update($validated);

        return redirect()->route('employees.show', $employee)
                        ->with('success', 'Employee updated successfully.');
    }

    /**
     * Remove the specified employee
     */
    public function destroy(Employee $employee)
    {
        $employee->delete();

        return redirect()->route('employees.index')
                        ->with('success', 'Employee deleted successfully.');
    }
}
