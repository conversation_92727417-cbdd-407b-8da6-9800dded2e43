import '../css/app.css';
import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import type { DefineComponent } from 'vue';
import { createApp, h } from 'vue';
import { ZiggyVue } from 'ziggy-js';
import { initializeTheme } from './composables/useAppearance';
import axios from 'axios';
import PrimeVue from 'primevue/config';
import ToastService from 'primevue/toastservice';
import InputText from 'primevue/inputtext';
import Button from 'primevue/button';
import Select from 'primevue/select';
import Tooltip from 'primevue/tooltip';
import Toast from 'primevue/toast';

// Configure axios defaults for CSRF token
const token = document.head.querySelector('meta[name="csrf-token"]');
if (token) {
    axios.defaults.headers.common['X-CSRF-TOKEN'] = token.getAttribute('content');
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}

// Import PrimeVue v4 theme and styles
import Aura from '@primeuix/themes/aura';
import 'primeicons/primeicons.css';

// Import SVG as component (assuming you've set up ?component imports in vite.config.ts)


const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

// Configure Inertia.js to include CSRF token in all requests
import { router } from '@inertiajs/vue3';
router.on('before', (event) => {
    const token = document.head.querySelector('meta[name="csrf-token"]');
    if (token && event.detail.visit.data instanceof FormData) {
        event.detail.visit.data.append('_token', token.getAttribute('content') || '');
    } else if (token && typeof event.detail.visit.data === 'object' && event.detail.visit.data !== null) {
        (event.detail.visit.data as any)._token = token.getAttribute('content') || '';
    }
});

createInertiaApp({
    title: (title) => (title ? `${title} - ${appName}` : appName),
    resolve: (name) => resolvePageComponent(
        `./pages/${name}.vue`, 
        import.meta.glob<DefineComponent>('./pages/**/*.vue')
    ),
    setup({ el, App, props, plugin }) {
        const app = createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue)
            .use(PrimeVue, {
                theme: {
                    preset: Aura,
                    options: {
                        prefix: 'p',
                        darkModeSelector: '.dark',
                        cssLayer: false
                    }
                }
            })
            .use(ToastService)
            .component('Toast', Toast)               
            .component('InputText', InputText)
            .component('Button', Button)
            .component('Select', Select)
            // Register SVG component globally
        
            .directive('tooltip', Tooltip);

        app.mount(el);
        initializeTheme();
    },
    progress: {
        color: '#4B5563',
    },
});