<template>
 
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-6xl mx-auto">
      <Toast  />
  <!-- //DIGITAL CLOCK --> 
  <div class="bg-gray-900 text-white rounded-md shadow-md px-3 py-2 flex flex-col items-center">
  <p class="text-xl md:text-2xl font-semibold">{{ date }}</p>
  <p class="font-mono text-4xl md:text-5xl tracking-widest mt-1">
    {{ weekday }} {{ digital_time }}
    <span :class="ampm === 'AM' ? 'text-blue-400' : 'text-orange-400'" class="ml-2">{{ ampm }}</span>
  </p>
</div>
      
      <!-- Action Buttons -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button 
            unstyled 
            @click="setType('check_in')" 
            :class="getActionButtonClass('check_in') + ' py-14'">
          
            <div class="flex flex-col items-center gap-2">
              <span class="text-lg md:text-4xl font-semibold">Check In</span>
            </div>
          </Button>

          <Button 
            unstyled 
            @click="setType('check_out')" 
            :class="getActionButtonClass('check_out')">
            <div class="flex flex-col items-center gap-2">
              <span class="text-lg md:text-4xl font-semibold">Check Out</span>
            </div>
          </Button>

          <Button 
            unstyled 
            @click="setType('break_in')" 
            :class="getActionButtonClass('break_in')">
            <div class="flex flex-col items-center gap-2">
              <span class="text-lg md:text-4xl font-semibold">Break In</span>
            </div>
          </Button>

          <Button 
            unstyled 
            @click="setType('break_out')" 
            :class="getActionButtonClass('break_out')">
          
            <div class="flex flex-col items-center gap-2">
              <span class="text-lg md:text-4xl font-semibold">Break Out</span>
            </div>
          </Button>
        </div>
      </div>
      
      <!-- Barcode Scanner -->
      <div v-if="Emp_Type" class="bg-white rounded-lg shadow-md p-4 md:p-8 mb-8 w-full">
        <h2 class="text-lg md:text-xl font-semibold mb-4 text-gray-800 text-center md:text-left">
          Scan Employee Code for: <span class="text-blue-600">{{ getActionLabel(Emp_Type) }}</span>
        </h2>

        <form @submit.prevent="handleFormSubmit" class="space-y-4">
          <div class="relative">
            <InputText 
              v-model="employee_code" 
              ref="barcodeInput" 
              placeholder="Scan or enter employee barcode..."
              class="w-full text-lg p-3 pr-12" 
              autocomplete="off"
              autocapitalize="off"
              spellcheck="false"
              inputmode="none"
              @keydown.enter="handleEnterKey" 
              @input="handleInputEvent"
              @blur="(e) => { handleBlurEvent(e); /* immediate refocus */ requestAnimationFrame(() => focusInput()) }"
              :class="{ 
                'border-red-300 focus:border-red-500': employee_code && employee_code.length < 3,
                'border-green-300 focus:border-green-500': employee_code && employee_code.length >= 3
              }"
            />
            <i class="pi pi-qrcode absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          </div>

 

          <div class="flex flex-col sm:flex-row gap-3">
            <Button 
              type="button" 
              label="Submit" 
              icon="pi pi-check" 
              :loading="isSubmitting" 
              class="p-button-success flex-1 sm:flex-none min-h-[48px]" 
              @click="handleFormSubmit" 
            />
            <Button 
              type="button" 
              label="Clear" 
              icon="pi pi-times" 
              @click="clearForm" 
              class="p-button-secondary flex-1 sm:flex-none min-h-[48px]"
            />
          </div>
        </form>
      </div>
      
      <!-- Employee Details with Click-to-Focus -->
      <div v-if="employeeDetails" 
           class="bg-white rounded-lg shadow-md p-4 md:p-6 mb-6 cursor-pointer hover:shadow-lg transition-shadow"
           @click="focusInput"
           title="Click to focus input for next scan">
        <h2 class="text-lg md:text-xl font-semibold mb-4 text-gray-700 flex items-center justify-center md:justify-start">
          <i class="pi pi-user mr-2"></i>
          Employee Details
        
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Photo Section -->
          <div class="flex justify-center md:justify-start">
              <!-- <div class="w-24 h-24 md:w- aspect-square relative md:h-full rounded-lg overflow-hidden bg-gray-100 border-2 border-gray-200"> -->
            <div class="w-auto h-auto md:w- aspect-square relative md:h-full rounded-lg overflow-hidden bg-gray-100 border-2 border-gray-200">
              <img v-if="employeeDetails.EMP_PhotoPath" :src="employeeDetails.EMP_PhotoPath" :alt="employeeDetails.EMP_FullName"
                class="w-full h-full object-cover" @error="handleImageError" />
              <div v-else class="w-full h-full flex items-center justify-center text-gray-400">
                <i class="pi pi-user text-3xl md:text-4xl"></i>
              </div>
            </div>
          </div>

          <!-- Employee Info -->
          <div class="space-y-3 text-center md:text-left">
            <div>
              <label class="text-sm font-medium text-gray-600">Full Name</label>
              <p class="text-lg font-semibold text-gray-800">{{ employeeDetails.EMP_FullName }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-600">Employee Number</label>
              <p class="text-gray-800">{{ employeeDetails.EMP_EmpNo || 'Not assigned' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-600">Department</label>
              <p class="text-gray-800">{{ employeeDetails.EMP_Department || 'Not assigned' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-600">Status</label>
              <div class="flex justify-center md:justify-start mt-1">
                <span :class="getEmployeeStatusClass(employeeDetails.EMP_IsActive)"
                  class="px-2 py-1 rounded-full text-xs font-medium">
                  {{ employeeDetails.EMP_IsActive === 1 ? 'Active' : 'Inactive' }}
                </span>
              </div>
            </div>
          </div>

          <!-- Attendance Status -->
          <div class="space-y-3 text-center md:text-left">
            <!-- <div>
              <label class="text-sm font-medium text-gray-600">Current Status</label>
              <div class="flex justify-center md:justify-start mt-1">
                <div
                  :class="getCurrentStatusInfo().class"
                  class="px-3 py-1 rounded-full text-sm font-medium">
                  {{ getCurrentStatusInfo().status }}
                </div>
              </div>
            </div> -->

            <div v-if="employeeDetails.last_action">
              <label class="text-sm font-medium text-gray-600">Last Action</label>
              <p class="text-sm text-gray-800">{{ employeeDetails.last_action.action_label }}</p>
              <p class="text-xs text-gray-500">{{ employeeDetails.last_action.logged_at }}</p>
            </div>

            <!-- <div v-if="getSuggestedAction()" class="mt-4">
              <div :class="getSuggestedActionClass()" class="px-3 py-2 rounded-lg text-sm">
                <strong>Next Action:</strong> {{ getSuggestedAction() }}
              </div>
            </div> -->
          </div>
        </div>
      </div>
      
      <!-- Loading State -->
      <div v-if="isLookingUp" class="bg-white rounded-lg shadow-md p-6 mb-6 text-center hidden">
        <div class="flex items-center justify-center space-x-2">
          <i class="pi pi-spin pi-spinner text-blue-500"></i>
          <span class="text-gray-600">Looking up employee...</span>
        </div>
      </div>
      
      <!-- Status Messages -->
      <div v-if="flash.success || flash.error || flash.warning" class="mb-6">
        <div v-if="flash.success"
          class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg flex items-center">
          <i class="pi pi-check-circle mr-2"></i>
          {{ flash.success }}
        </div>
        <div v-if="flash.error"
          class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg flex items-center">
          <i class="pi pi-exclamation-triangle mr-2"></i>
          {{ flash.error }}
        </div>
        <div v-if="flash.warning"
          class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg flex items-center">
          <i class="pi pi-exclamation-triangle mr-2"></i>
          {{ flash.warning }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, watch } from 'vue'
import { usePage } from '@inertiajs/vue3'
import { useToast } from 'primevue/usetoast'
import { route } from 'ziggy-js'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Toast from 'primevue/toast'

// Import our composables
import { useClock } from '@/composables/useClock'
import { useEmployeeScanner } from '@/composables/useEmployeeScanner'
import { useBarcodeScanner } from '@/composables/useBarcodeScanner'
// import { useEmployeeLookup } from '@/composables/useEmployeeLookup'
import { useAttendanceSubmission } from '@/composables/useAttendanceSubmission'

// Clock functionality
const { digital_time, date, weekday, ampm } = useClock()

// Employee scanner functionality
const {
  employee_code,
  Emp_Type,
  employeeDetails,
  isSubmitting,
  isLookingUp,
  isFromBarcodeScan,
  getCurrentState,
  getSuggestedActions,
  getActionButtonClass,
  getEmployeeStatusClass,
  getActionLabel,
  playSuccessSound,
  cleanup: cleanupScanner,
  ACTION_LABELS,
  employeeDetailsTimer,
  flashMessageTimer
} = useEmployeeScanner()

// Barcode scanner functionality
const {
  barcodeInput,
  focusInput,
  setupEmployeeLookup,
  handleInputEvent,
  handleEnterKey: handleEnterKeyBase,
  handleBlurEvent: handleBlurEventBase,
  cleanup: cleanupBarcode,
  lastInputTime,
  inputStartTime,
  lastSubmissionTime
} = useBarcodeScanner()

// Employee lookup functionality (using local implementation for now)
// const { lookupEmployee } = useEmployeeLookup()

// Attendance submission functionality
const { submit, handleFormSubmit } = useAttendanceSubmission(
  employee_code,
  Emp_Type,
  employeeDetails,
  isSubmitting,
  isFromBarcodeScan,
  inputStartTime,
  lastSubmissionTime,
  focusInput,
  ACTION_LABELS
)

// Configure the barcode scanner handlers AFTER handleFormSubmit is defined
const handleEnterKey = handleEnterKeyBase(
  employee_code,
  employeeDetails,
  isLookingUp,
  Emp_Type,
  lookupEmployeeLocal,
  handleFormSubmit
)

const handleBlurEvent = handleBlurEventBase(
  employee_code,
  employeeDetails,
  isLookingUp,
  Emp_Type,
  lookupEmployeeLocal,
  submit
)

// Page and flash messages
const page = usePage()
const flash = computed(() => page.props.flash || {})
const toast = useToast()

// --- Always-on focus helpers ---
const ensureFocus = () => {
  // Delegate to existing composable method
  try { focusInput() } catch (e) {}
}
const scheduleRefocus = () => {
  requestAnimationFrame(ensureFocus)
  setTimeout(ensureFocus, 0)
}
const onDocMouseDown = () => scheduleRefocus()
const onDocClick = () => scheduleRefocus()
const onKeyDownCapture = () => ensureFocus()
const onWindowFocus = () => scheduleRefocus()
const onVisibilityChange = () => {
  if (document.visibilityState === 'visible') scheduleRefocus()
}

onMounted(() => {
  // Initial focus
  scheduleRefocus()
  // Capture phase to preempt other handlers
  document.addEventListener('mousedown', onDocMouseDown, true)
  document.addEventListener('click', onDocClick, true)
  document.addEventListener('keydown', onKeyDownCapture, true)
  window.addEventListener('focus', onWindowFocus)
  document.addEventListener('visibilitychange', onVisibilityChange)
})

onUnmounted(() => {
  document.removeEventListener('mousedown', onDocMouseDown, true)
  document.removeEventListener('click', onDocClick, true)
  document.removeEventListener('keydown', onKeyDownCapture, true)
  window.removeEventListener('focus', onWindowFocus)
  document.removeEventListener('visibilitychange', onVisibilityChange)
})

watch(Emp_Type, (newType) => {
  if (newType) {
    focusInput()
  }
})

// Auto-lookup employee when typing (real-time with debounce)
let lookupTimeout = null
watch(employee_code, (newCode, oldCode) => {
  console.log('Watcher triggered with newCode:', newCode, 'oldCode:', oldCode)
  
  const currentTime = Date.now()
  
  // Clear any existing lookup timeout
  if (lookupTimeout) {
    clearTimeout(lookupTimeout)
  }
  

  const cleanCode = newCode.replace(/[\n\r\t]/g, '').trim()
  console.log('Cleaned code:', cleanCode)
  
  // Validate code format (basic validation)
  if (cleanCode && !/^[A-Za-z0-9]+$/.test(cleanCode)) {
    console.log('Invalid characters in employee code:', cleanCode)
    return
  }
  
  // Track input timing for barcode detection
  if (!oldCode || oldCode.length === 0) {
    inputStartTime.value = currentTime
  }
  lastInputTime.value = currentTime
  
  // Check if this is from a barcode scan
  const hasNewline = newCode.includes('\n')
  const isLongCode = cleanCode.length >= 6 // Reduced from 10 to 6 for shorter employee codes
  const inputDuration = currentTime - inputStartTime.value
  const isQuickInput = inputDuration < 300 && cleanCode.length >= 4 // More lenient detection

  isFromBarcodeScan.value = hasNewline || isLongCode || isQuickInput || isFromBarcodeScan.value
  console.log('Is from barcode scan:', isFromBarcodeScan.value, {
    hasNewline,
    isLongCode,
    inputDuration,
    isQuickInput,
    cleanCodeLength: cleanCode.length,
    oldCodeLength: oldCode?.length || 0
  })
  
  // Update the employee_code with cleaned value if different
  if (cleanCode !== newCode) {
    employee_code.value = cleanCode
    return
  }
  
  // Don't clear employee details when code is empty - let the timer handle it
  if (cleanCode === '') {
    isFromBarcodeScan.value = false
    inputStartTime.value = 0
    return
  }
  
  // Lookup employee with shorter delay for better responsiveness
  if (cleanCode && cleanCode.length >= 3) {
    console.log('Setting up lookup timeout for:', cleanCode)
    const delay = isFromBarcodeScan.value ? 100 : 200
    lookupTimeout = setTimeout(() => {
      const currentCode = employee_code.value.replace(/\n/g, '').trim()
      console.log('Timeout executing, checking if code still matches:', currentCode, 'vs', cleanCode)
      if (currentCode === cleanCode) {
        console.log('Calling lookupEmployee from watcher with:', cleanCode)
        lookupEmployeeLocal(cleanCode)
      }
    }, delay)
  }
})

function setType(type) {
  Emp_Type.value = type
  // Clear employee code immediately for new input, but keep employee details visible
  employee_code.value = ''
  isFromBarcodeScan.value = false
  inputStartTime.value = 0
  focusInput()
}




function clearForm() {
  cleanupScanner()
  cleanupBarcode()

  employee_code.value = ''
  Emp_Type.value = ''
  employeeDetails.value = null
  isFromBarcodeScan.value = false
  inputStartTime.value = 0
  lastInputTime.value = 0
}

// Create a local lookup function that works with our current setup
async function lookupEmployeeLocal(bioId) {
  console.log('lookupEmployee called with bioId:', bioId, 'length:', bioId?.length)
  if (!bioId) {
    console.log('Returning early - bioId empty')
    return
  }

  // Check if we're looking up the same employee
  const isSameEmployee = employeeDetails.value &&
    (employeeDetails.value.EMP_BioID === bioId ||
     employeeDetails.value.EMP_EmpNo === bioId ||
     employeeDetails.value.EMP_FullName === bioId)

  if (isSameEmployee) {
    console.log('Same employee detected - skipping lookup, keeping current details')

    // Check if the same employee is active before allowing submission
    if (employeeDetails.value.EMP_IsActive === 0) {
      console.log('Same employee is inactive - blocking attendance logging')
      toast.add({
        severity: 'warn',
        summary: 'Employee Inactive',
        detail: 'This user is inactive',
        life: 4000
      })

      // Clear employee code to prevent any submission attempts
      employee_code.value = ''
      isFromBarcodeScan.value = false
      return
    }

    // Still handle barcode scan auto-submission for same active employee
    if (isFromBarcodeScan.value && Emp_Type.value) {
      console.log('Barcode scan detected for same active employee - setting up auto-submission')
      setTimeout(() => {
        if (employeeDetails.value && employee_code.value.trim() && Emp_Type.value) {
          console.log('Auto-submitting barcode scan for same employee')
          submit()
        }
      }, 500)
    }
    return
  }

  console.log('Starting employee lookup for:', bioId, 'isFromBarcodeScan:', isFromBarcodeScan.value)

  // Clear current employee details only when looking up a different employee
  if (employeeDetails.value) {
    console.log('Clearing previous employee details for new lookup')
    employeeDetails.value = null
  }

  isLookingUp.value = true

  try {
    const response = await fetch(route('employee.lookup', bioId))
    const data = await response.json()
    console.log('Lookup response:', data)

    if (data.success) {
      console.log('Employee found:', data.employee)
      employeeDetails.value = data.employee

      // Check if employee is active (EMP_IsActive = 1)
      if (data.employee.EMP_IsActive === 0) {
        console.log('Employee is inactive - blocking attendance logging')
        toast.add({
          severity: 'warn',
          summary: 'Employee Inactive',
          detail: 'This user is inactive',
          life: 4000
        })

        // Clear employee code to prevent any submission attempts
        employee_code.value = ''
        isFromBarcodeScan.value = false

        // Do not proceed with auto-submission for inactive employees
        return
      }

      // Show employee found toast for active employees
      toast.add({
        severity: 'info',
        summary: 'Employee Found',
        detail: `${data.employee.EMP_FullName} loaded successfully`,
        life: 2000
      })

      // Handle barcode scan auto-submission - only for active employees
      console.log('Checking auto-submission conditions:', {
        isFromBarcodeScan: isFromBarcodeScan.value,
        Emp_Type: Emp_Type.value,
        employee_code: employee_code.value.trim(),
        isActive: data.employee.EMP_IsActive
      })

      if (isFromBarcodeScan.value && Emp_Type.value) {
        console.log('Barcode scan detected - setting up auto-submission for active employee')
        // Auto-submit immediately after showing employee details
        setTimeout(() => {
          if (employeeDetails.value && employee_code.value.trim() && Emp_Type.value) {
            console.log('Auto-submitting barcode scan')
            submit()
          } else {
            console.log('Auto-submission failed - missing conditions:', {
              employeeDetails: !!employeeDetails.value,
              employee_code: employee_code.value.trim(),
              Emp_Type: Emp_Type.value
            })
          }
        }, 300) // Reduced delay for faster auto-submission
      } else {
        console.log('Auto-submission skipped - conditions not met:', {
          isFromBarcodeScan: isFromBarcodeScan.value,
          Emp_Type: Emp_Type.value
        })
      }
    } else {
      console.log('Employee not found')
      employeeDetails.value = null
      toast.add({
        severity: 'error',
        summary: 'Employee Not Found',
        detail: `No employee found with code: ${bioId}`,
        life: 3000
      })
    }
  } catch (error) {
    console.error('Error looking up employee:', error)
    employeeDetails.value = null
    toast.add({
      severity: 'error',
      summary: 'Lookup Error',
      detail: 'Failed to lookup employee. Please try again.',
      life: 3000
    })
  } finally {
    // Minimal delay to prevent UI flicker
    setTimeout(() => {
      isLookingUp.value = false
    }, 200)
  }
}

// function getActionLabel(actionType) {
//   const labels = {
//     'check_in': 'Check In',
//     'check_out': 'Check Out',
//     'break_in': 'Break In',
//     'break_out': 'Break Out'
//   }
//   return labels[actionType] || actionType
// }

// // Auto-clear flash messages after 5 seconds
// watch(flash, (newFlash) => {
//   if (newFlash.success || newFlash.error || newFlash.warning) {
//     // Clear any existing timer
//     if (flashMessageTimer.value) {
//       clearTimeout(flashMessageTimer.value)
//     }
    
//     // Set new timer to clear flash messages
//     flashMessageTimer.value = setTimeout(() => {
//       router.get(window.location.pathname, {}, {
//         preserveState: true,
//         preserveScroll: true,
//         only: ['flash']
//       })
//     }, 5000) // 5 seconds
//   }
// }, { deep: true })

// // Cleanup function
// function cleanup() {
//   if (lookupTimeout) {
//     clearTimeout(lookupTimeout)
//     lookupTimeout = null
//   }
  
//   if (employeeDetailsTimer.value) {
//     clearTimeout(employeeDetailsTimer.value)
//     employeeDetailsTimer.value = null
//   }
  
//   if (flashMessageTimer.value) {
//     clearTimeout(flashMessageTimer.value)
//     flashMessageTimer.value = null
//   }
// }

// function getEmployeeStatusClass(status) {
//   const classes = {
//     'active': 'bg-green-100 text-green-800',
//     'inactive': 'bg-gray-100 text-gray-800',
//     'suspended': 'bg-red-100 text-red-800'
//   }
//   return classes[status] || 'bg-gray-100 text-gray-800'
// }

// function getSuggestedAction() {
//   if (!employeeDetails.value) return ''

//   const suggestedActions = getSuggestedActions()
//   if (suggestedActions.length === 0) return ''
  
//   if (suggestedActions.length === 1) {
//     return ACTION_LABELS[suggestedActions[0]]
//   }
  
//   // Multiple suggestions - show the most logical one first
//   const actionLabels = suggestedActions.map(action => ACTION_LABELS[action])
//   return actionLabels.join(' or ')
// }

// function getSuggestedActionClass() {
//   if (!employeeDetails.value) return 'bg-gray-100 text-gray-800'

//   const isCheckedIn = employeeDetails.value.is_checked_in
//   return isCheckedIn ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
// }

function handleImageError(event) {
  // Hide broken image and show placeholder
  event.target.style.display = 'none'
}

onMounted(() => {
  // Focus on the page load if there's an action selected
  if (Emp_Type.value) {
    focusInput()
  }
})

onUnmounted(() => {
  cleanupScanner()
  cleanupBarcode()
})
</script>
